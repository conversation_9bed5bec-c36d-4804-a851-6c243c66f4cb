<?php
/**
 * Plugin Name: <PERSON>sque<PERSON>u Senha
 * Description: Plugin personalizado para customizar a tela de esqueceu a senha do WordPress com design white label e templates de email
 * Version: 1.1.1
 * Author: Astra Child Theme
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

// Incluir arquivo de instalação
require_once __DIR__ . '/install.php';

class CustomPasswordReset {
    
    private $plugin_path;
    private $plugin_url;
    
    public function __construct() {
        $this->plugin_path = plugin_dir_path(__FILE__);
        $this->plugin_url = plugin_dir_url(__FILE__);

        // Executar instalação/upgrade se necessário
        CustomPasswordResetInstaller::upgrade();

        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Hook para interceptar a página de reset de senha
        add_action('login_form_lostpassword', [$this, 'custom_lost_password_page']);
        add_action('login_form_retrievepassword', [$this, 'custom_lost_password_page']);
        add_action('login_form_resetpass', [$this, 'custom_reset_password_page']);
        add_action('login_form_rp', [$this, 'custom_reset_password_page']);
        
        // Enfileirar scripts e estilos
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('login_enqueue_scripts', [$this, 'enqueue_login_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        // Menu de administração
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        
        // AJAX handlers
        add_action('wp_ajax_cpr_save_settings', [$this, 'save_settings_ajax']);
        add_action('wp_ajax_cpr_reset_settings', [$this, 'reset_settings_ajax']);
        add_action('wp_ajax_cpr_send_test_email', [$this, 'send_test_email_ajax']);
        add_action('wp_ajax_cpr_generate_email_preview', [$this, 'generate_email_preview_ajax']);
        
        // Processar formulário customizado
        add_action('wp_loaded', [$this, 'process_custom_lost_password']);
        add_action('wp_loaded', [$this, 'process_custom_reset_password']);

        // Shortcode para usar em páginas
        add_shortcode('custom_password_reset', [$this, 'shortcode_password_reset']);
        
        // Hooks para personalização de email
        add_filter('retrieve_password_message', [$this, 'custom_password_reset_email'], 10, 4);
        add_filter('wp_mail_content_type', [$this, 'set_html_content_type']);
        add_filter('retrieve_password_title', [$this, 'custom_password_reset_subject'], 10, 3);
    }
    
    public function enqueue_assets() {
        // Carrega sempre que estivermos em páginas relacionadas
        if ($this->is_password_reset_page() || is_page() || isset($_GET['action'])) {
            wp_enqueue_style(
                'custom-password-reset-style',
                $this->plugin_url . 'assets/css/password-reset.css',
                [],
                time() // Força atualização durante desenvolvimento
            );

            wp_enqueue_script(
                'custom-password-reset-script',
                $this->plugin_url . 'assets/js/password-reset.js',
                ['jquery'],
                time(), // Força atualização durante desenvolvimento
                true
            );

            // Localizar script com configurações
            wp_localize_script('custom-password-reset-script', 'cprSettings', [
                'theme' => get_option('cpr_theme', 'dark'),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('cpr_nonce')
            ]);
        }
    }
    
    public function enqueue_login_assets() {
        wp_enqueue_style(
            'custom-password-reset-login',
            $this->plugin_url . 'assets/css/login-override.css',
            [],
            filemtime($this->plugin_path . 'assets/css/login-override.css')
        );
        
        wp_enqueue_script(
            'custom-password-reset-login-js',
            $this->plugin_url . 'assets/js/login-override.js',
            ['jquery'],
            filemtime($this->plugin_path . 'assets/js/login-override.js'),
            true
        );
    }

    public function enqueue_admin_assets($hook) {
        // Só carrega na página do plugin
        if ($hook !== 'settings_page_custom-password-reset') {
            return;
        }

        // Enfileirar estilos administrativos
        wp_enqueue_style(
            'custom-password-reset-admin',
            $this->plugin_url . 'assets/css/admin.css',
            ['wp-color-picker'],
            filemtime($this->plugin_path . 'assets/css/admin.css')
        );

        // Enfileirar scripts administrativos
        wp_enqueue_script(
            'custom-password-reset-admin',
            $this->plugin_url . 'assets/js/admin.js',
            ['jquery', 'wp-color-picker', 'media-upload', 'thickbox'],
            filemtime($this->plugin_path . 'assets/js/admin.js'),
            true
        );

        // Enfileirar media uploader
        wp_enqueue_media();
    }
    
    private function is_password_reset_page() {
        global $pagenow;

        // Verifica se estamos no wp-login.php com ações de reset
        if ($pagenow === 'wp-login.php' && isset($_GET['action']) &&
            in_array($_GET['action'], ['lostpassword', 'retrievepassword', 'resetpass', 'rp'])) {
            return true;
        }

        // Verifica se estamos em páginas personalizadas
        if (is_page('esqueceu-senha') || is_page('redefinir-senha')) {
            return true;
        }

        // Verifica se a URL contém parâmetros de reset
        if (isset($_GET['key']) && isset($_GET['login'])) {
            return true;
        }

        return false;
    }
    
    public function custom_lost_password_page() {
        // Verificar se é preview do admin
        if (isset($_GET['cpr_preview']) && current_user_can('manage_options')) {
            // Para preview, sempre renderizar o formulário personalizado
            $this->render_custom_lost_password_form();
            return;
        }

        // Verificar se devemos usar página personalizada
        $custom_page = get_option('cpr_custom_page_slug', '');
        $enable_custom = get_option('cpr_enable_custom_page', false);

        if ($enable_custom && !empty($custom_page) && !is_page($custom_page)) {
            wp_redirect(site_url('/' . $custom_page . '/'));
            exit;
        }

        // Renderizar formulário personalizado
        $this->render_custom_lost_password_form();
    }
    
    public function custom_reset_password_page() {
        $this->render_custom_reset_password_form();
    }
    
    private function render_custom_lost_password_form() {
        $settings = $this->get_settings();

        // Verificar se o template existe
        $template_path = $this->plugin_path . 'templates/lost-password-form.php';
        if (!file_exists($template_path)) {
            wp_die('Template de formulário não encontrado: ' . $template_path);
        }

        include $template_path;
        exit;
    }
    
    private function render_custom_reset_password_form() {
        $settings = $this->get_settings();

        // Verificar se o template existe
        $template_path = $this->plugin_path . 'templates/reset-password-form.php';
        if (!file_exists($template_path)) {
            wp_die('Template de reset não encontrado: ' . $template_path);
        }

        include $template_path;
        exit;
    }
    
    public function process_custom_lost_password() {
        if (isset($_POST['cpr_lost_password_submit'])) {
            $this->handle_lost_password_submission();
        }
    }
    
    public function process_custom_reset_password() {
        if (isset($_POST['cpr_reset_password_submit'])) {
            $this->handle_reset_password_submission();
        }
    }
    
    private function handle_lost_password_submission() {
        if (!wp_verify_nonce($_POST['cpr_nonce'], 'cpr_lost_password')) {
            wp_die('Erro de segurança');
        }
        
        $user_login = sanitize_text_field($_POST['user_login']);
        
        if (empty($user_login)) {
            $this->add_error('Por favor, insira seu nome de usuário ou e-mail.');
            return;
        }
        
        // Processar reset de senha usando funções nativas do WordPress
        $result = retrieve_password($user_login);
        
        if (is_wp_error($result)) {
            $this->add_error($result->get_error_message());
        } else {
            $this->add_success('Instruções de redefinição de senha foram enviadas para seu e-mail.');
        }
    }
    
    private function handle_reset_password_submission() {
        if (!wp_verify_nonce($_POST['cpr_nonce'], 'cpr_reset_password')) {
            wp_die('Erro de segurança');
        }
        
        $key = sanitize_text_field($_POST['key']);
        $login = sanitize_text_field($_POST['login']);
        $password = $_POST['pass1'];
        $password_confirm = $_POST['pass2'];
        
        if (empty($password) || empty($password_confirm)) {
            $this->add_error('Por favor, preencha ambos os campos de senha.');
            return;
        }
        
        if ($password !== $password_confirm) {
            $this->add_error('As senhas não coincidem.');
            return;
        }
        
        $user = check_password_reset_key($key, $login);
        
        if (is_wp_error($user)) {
            $this->add_error('Link de redefinição inválido ou expirado.');
            return;
        }
        
        reset_password($user, $password);
        $this->add_success('Senha redefinida com sucesso! Você pode fazer login agora.');
    }
    
    private function add_error($message) {
        if (!session_id()) {
            session_start();
        }
        $_SESSION['cpr_errors'][] = $message;
    }
    
    private function add_success($message) {
        if (!session_id()) {
            session_start();
        }
        $_SESSION['cpr_success'] = $message;
    }
    
    public function get_errors() {
        if (!session_id()) {
            session_start();
        }
        $errors = isset($_SESSION['cpr_errors']) ? $_SESSION['cpr_errors'] : [];
        unset($_SESSION['cpr_errors']);
        return $errors;
    }
    
    public function get_success() {
        if (!session_id()) {
            session_start();
        }
        $success = isset($_SESSION['cpr_success']) ? $_SESSION['cpr_success'] : '';
        unset($_SESSION['cpr_success']);
        return $success;
    }
    
    private function get_settings() {
        return [
            'theme' => get_option('cpr_theme', 'dark'),
            'logo_url' => get_option('cpr_logo_url', ''),
            'background_color' => get_option('cpr_background_color', '#1a1a1a'),
            'card_background' => get_option('cpr_card_background', '#2d2d2d'),
            'text_color' => get_option('cpr_text_color', '#ffffff'),
            'button_color' => get_option('cpr_button_color', '#4a90e2'),
            'button_text_color' => get_option('cpr_button_text_color', '#ffffff'),
            'button_hover_color' => get_option('cpr_button_hover_color', '#357abd'),

            'title' => get_option('cpr_title', 'Recuperar Acesso'),
            'subtitle' => get_option('cpr_subtitle', 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.'),
            'button_text' => get_option('cpr_button_text', 'Enviar Instruções'),
            'back_to_login_text' => get_option('cpr_back_to_login_text', 'Voltar ao Login'),
            'success_message' => get_option('cpr_success_message', 'Instruções de redefinição de senha foram enviadas para seu e-mail.'),
            'input_placeholder' => get_option('cpr_input_placeholder', 'E-mail corporativo'),
            'custom_page_slug' => get_option('cpr_custom_page_slug', ''),
            'enable_custom_page' => get_option('cpr_enable_custom_page', false),
            'custom_css' => get_option('cpr_custom_css', ''),
            'custom_js' => get_option('cpr_custom_js', ''),

            // Configurações de email
            'email_enabled' => get_option('cpr_email_enabled', false),
            'email_subject' => get_option('cpr_email_subject', 'Redefinir sua senha'),
            'email_from_name' => get_option('cpr_email_from_name', get_bloginfo('name')),
            'email_from_email' => get_option('cpr_email_from_email', get_option('admin_email')),
            'email_logo_url' => get_option('cpr_email_logo_url', ''),
            'email_header_color' => get_option('cpr_email_header_color', '#4a90e2'),
            'email_button_color' => get_option('cpr_email_button_color', '#4a90e2'),
            'email_footer_text' => get_option('cpr_email_footer_text', 'Este é um email automático, não responda.'),
            'email_greeting' => get_option('cpr_email_greeting', 'Olá'),
            'email_main_text' => get_option('cpr_email_main_text', 'Recebemos uma solicitação para redefinir a senha da sua conta em {site_name}.'),
            'email_instruction_text' => get_option('cpr_email_instruction_text', 'Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:'),
            'email_button_text' => get_option('cpr_email_button_text', 'Redefinir Minha Senha'),
            'email_signature' => get_option('cpr_email_signature', 'Atenciosamente,<br>Equipe {site_name}'),
            'email_signature_avatar' => get_option('cpr_email_signature_avatar', ''),
            'email_footer_text' => get_option('cpr_email_footer_text', 'Este é um email automático, não responda.')
        ];
    }
    
    public function add_admin_menu() {
        add_options_page(
            'Esqueceu Senha',
            'Esqueceu Senha',
            'manage_options',
            'custom-password-reset',
            [$this, 'admin_page']
        );
    }
    
    public function register_settings() {
        $settings = [
            'cpr_theme', 'cpr_logo_url', 'cpr_background_color', 'cpr_card_background',
            'cpr_text_color', 'cpr_button_color', 'cpr_button_text_color',
            'cpr_title', 'cpr_subtitle', 'cpr_button_text', 'cpr_back_to_login_text',
            'cpr_custom_page_slug', 'cpr_enable_custom_page', 'cpr_custom_css', 'cpr_custom_js',
            // Configurações de email
            'cpr_email_enabled', 'cpr_email_subject', 'cpr_email_from_name', 'cpr_email_from_email',
            'cpr_email_logo_url', 'cpr_email_header_color', 'cpr_email_button_color', 'cpr_email_footer_text',
            'cpr_email_greeting', 'cpr_email_main_text', 'cpr_email_instruction_text', 'cpr_email_button_text',
            'cpr_email_signature', 'cpr_email_signature_avatar', 'cpr_email_footer_text'
        ];

        foreach ($settings as $setting) {
            register_setting('cpr_settings', $setting);
        }
    }
    
    public function admin_page() {
        $settings = $this->get_settings();
        include $this->plugin_path . 'templates/admin-page.php';
    }
    
    public function save_settings_ajax() {
        check_ajax_referer('cpr_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $settings = $_POST['settings'];
        $is_preview = isset($_POST['preview']) && $_POST['preview'] === '1';

        // Checkboxes que precisam de tratamento especial
        $checkboxes = ['enable_custom_page', 'email_enabled'];
        
        // Primeiro, resetar checkboxes
        foreach ($checkboxes as $checkbox) {
            update_option('cpr_' . $checkbox, false);
        }

        // Processar os valores enviados
        foreach ($settings as $key => $value) {
            // Sanitizar valor baseado no tipo
            if (in_array($key, ['custom_css', 'custom_js', 'subtitle'])) {
                $sanitized_value = wp_kses_post($value);
            } elseif (in_array($key, $checkboxes)) {
                // Para checkboxes, converter para boolean
                $sanitized_value = ($value === '1' || $value === 'on') ? true : false;
            } else {
                $sanitized_value = sanitize_text_field($value);
            }

            update_option('cpr_' . $key, $sanitized_value);
        }

        // Limpar cache se necessário
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        $message = $is_preview ? 'Configurações aplicadas ao preview!' : 'Configurações salvas com sucesso!';
        wp_send_json_success($message);
    }
    
    public function reset_settings_ajax() {
        check_ajax_referer('cpr_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $defaults = [
            'cpr_theme' => 'dark',
            'cpr_logo_url' => '',
            'cpr_background_color' => '#1a1a1a',
            'cpr_card_background' => '#2d2d2d',
            'cpr_text_color' => '#ffffff',
            'cpr_button_color' => '#4a90e2',
            'cpr_button_text_color' => '#ffffff',
            'cpr_title' => 'Recuperar Acesso',
            'cpr_subtitle' => 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.',
            'cpr_button_text' => 'Enviar Instruções',
            'cpr_back_to_login_text' => 'Voltar ao Login',
            'cpr_custom_page_slug' => '',
            'cpr_enable_custom_page' => false,
            'cpr_custom_css' => '',
            'cpr_custom_js' => '',
            // Configurações de email
            'cpr_email_enabled' => false,
            'cpr_email_subject' => 'Redefinir sua senha',
            'cpr_email_from_name' => get_bloginfo('name'),
            'cpr_email_from_email' => get_option('admin_email'),
            'cpr_email_logo_url' => '',
            'cpr_email_header_color' => '#4a90e2',
            'cpr_email_button_color' => '#4a90e2',
            'cpr_email_footer_text' => 'Este é um email automático, não responda.',
            'cpr_email_greeting' => 'Olá',
            'cpr_email_main_text' => 'Recebemos uma solicitação para redefinir a senha da sua conta em {site_name}.',
            'cpr_email_instruction_text' => 'Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:',
            'cpr_email_button_text' => 'Redefinir Minha Senha',
            'cpr_email_signature' => 'Atenciosamente,<br>Equipe {site_name}',
            'cpr_email_signature_avatar' => '',
            'cpr_email_footer_text' => 'Este é um email automático, não responda.'
        ];
        
        foreach ($defaults as $key => $value) {
            update_option($key, $value);
        }
        
        wp_send_json_success('Configurações resetadas com sucesso!');
    }

    public function shortcode_password_reset($atts) {
        $atts = shortcode_atts([
            'type' => 'lost_password', // lost_password ou reset_password
            'redirect' => '',
        ], $atts);

        ob_start();

        if ($atts['type'] === 'reset_password' && isset($_GET['key']) && isset($_GET['login'])) {
            $this->render_custom_reset_password_form();
        } else {
            $this->render_custom_lost_password_form();
        }

        return ob_get_clean();
    }

    /**
     * Personaliza o email de redefinição de senha
     */
    public function custom_password_reset_email($message, $key, $user_login, $user_data) {
        // Verificar se a personalização está habilitada
        if (!get_option('cpr_email_enabled', false)) {
            return $message;
        }

        $reset_url = network_site_url("wp-login.php?action=rp&key=$key&login=" . rawurlencode($user_login), 'login');
        
        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_message = ob_get_clean();
            return $html_message;
        }

        return $message;
    }

    /**
     * Define o tipo de conteúdo como HTML para emails
     */
    public function set_html_content_type() {
        return 'text/html';
    }

    /**
     * Personaliza o assunto do email
     */
    public function custom_password_reset_subject($title, $user_login, $user_data) {
        $custom_subject = get_option('cpr_email_subject', '');
        if (!empty($custom_subject)) {
            return $custom_subject;
        }
        return $title;
    }

    /**
     * Envia email de teste via AJAX
     */
    public function send_test_email_ajax() {
        check_ajax_referer('cpr_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $test_email = sanitize_email($_POST['email']);
        if (!is_email($test_email)) {
            wp_send_json_error('Endereço de email inválido.');
        }

        // Criar dados fictícios para o teste
        $user_data = (object) [
            'display_name' => 'Usuário Teste',
            'user_email' => $test_email,
            'user_login' => 'teste'
        ];

        $key = 'teste123';
        $reset_url = network_site_url("wp-login.php?action=rp&key=$key&login=teste", 'login');

        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_message = ob_get_clean();

            $subject = get_option('cpr_email_subject', 'Redefinir sua senha - TESTE');
            $from_name = get_option('cpr_email_from_name', get_bloginfo('name'));
            $from_email = get_option('cpr_email_from_email', get_option('admin_email'));

            $headers = [
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $from_name . ' <' . $from_email . '>'
            ];

            $sent = wp_mail($test_email, $subject . ' (TESTE)', $html_message, $headers);

            if ($sent) {
                wp_send_json_success('Email de teste enviado com sucesso!');
            } else {
                wp_send_json_error('Erro ao enviar email de teste.');
            }
        } else {
            wp_send_json_error('Template de email não encontrado.');
        }
    }

    /**
     * Gera preview do email via AJAX
     */
    public function generate_email_preview_ajax() {
        check_ajax_referer('cpr_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $preview_settings = $_POST['settings'];
        
        // Temporariamente salvar as configurações para o preview
        $original_settings = [];
        foreach ($preview_settings as $key => $value) {
            $option_key = 'cpr_' . $key;
            $original_settings[$key] = get_option($option_key);
            update_option($option_key, sanitize_text_field($value));
        }

        // Criar dados fictícios para o preview
        $user_data = (object) [
            'display_name' => 'João Silva',
            'user_email' => '<EMAIL>',
            'user_login' => 'joao'
        ];

        $key = 'preview123';
        $reset_url = 'https://seusite.com/wp-login.php?action=rp&key=preview123&login=joao';

        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_preview = ob_get_clean();

            // Restaurar configurações originais
            foreach ($original_settings as $key => $value) {
                $option_key = 'cpr_' . $key;
                update_option($option_key, $value);
            }

            wp_send_json_success($html_preview);
        } else {
            wp_send_json_error('Template de email não encontrado.');
        }
    }
}

// Inicializar o plugin
new CustomPasswordReset();
