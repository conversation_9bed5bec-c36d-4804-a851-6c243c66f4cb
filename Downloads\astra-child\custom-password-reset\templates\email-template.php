<?php
/**
 * Template de Email para Redefinição de Senha
 * Template moderno, clean e minimal para emails de redefinição de senha
 */

if (!defined('ABSPATH')) {
    exit;
}

// Obter configurações
$settings = [
    'email_logo_url' => get_option('cpr_email_logo_url', ''),
    'email_header_color' => get_option('cpr_email_header_color', '#4a90e2'),
    'email_button_color' => get_option('cpr_email_button_color', '#4a90e2'),
    'email_footer_text' => get_option('cpr_email_footer_text', 'Este é um email automático, não responda.'),
    'email_from_name' => get_option('cpr_email_from_name', get_bloginfo('name')),
    'email_greeting' => get_option('cpr_email_greeting', 'Olá'),
    'email_main_text' => get_option('cpr_email_main_text', 'Recebemos uma solicitação para redefinir a senha da sua conta em {site_name}.'),
    'email_instruction_text' => get_option('cpr_email_instruction_text', 'Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:'),
    'email_button_text' => get_option('cpr_email_button_text', 'Redefinir Minha Senha'),
    'email_alternative_title' => get_option('cpr_email_alternative_title', 'Link não funcionando?'),
    'email_alternative_text' => get_option('cpr_email_alternative_text', 'Copie e cole este link no seu navegador:'),
    'email_security_title' => get_option('cpr_email_security_title', '🔒 Informações de Segurança'),
    'email_security_text' => get_option('cpr_email_security_text', 'Este link expira em 24 horas por motivos de segurança. Se você não solicitou esta redefinição, pode ignorar este email com segurança.'),
    'email_help_text' => get_option('cpr_email_help_text', 'Se você tiver alguma dúvida ou precisar de ajuda, entre em contato conosco.'),
    'email_signature' => get_option('cpr_email_signature', 'Atenciosamente,<br>Equipe {site_name}')
];

$site_name = get_bloginfo('name');
$site_url = get_site_url();

// Substituir placeholders
$settings['email_main_text'] = str_replace('{site_name}', $site_name, $settings['email_main_text']);
$settings['email_signature'] = str_replace('{site_name}', $site_name, $settings['email_signature']);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>Redefinir Senha - <?php echo esc_html($site_name); ?></title>
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }

        .email-wrapper {
            width: 100%;
            background-color: #f5f5f5;
            padding: 40px 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background-color: #ffffff;
            padding: 40px 40px 20px 40px;
            text-align: center;
            border-bottom: 2px solid #e5e5e5;
        }

        .lock-icon {
            width: 60px;
            height: 60px;
            background-color: <?php echo esc_attr($settings['email_header_color']); ?>;
            border-radius: 50%;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .lock-icon::before {
            content: "🔒";
            font-size: 24px;
            color: white;
        }

        .email-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
        }

        .email-header .subtitle {
            font-size: 14px;
            color: #718096;
            margin-bottom: 0;
        }

        .email-body {
            padding: 40px;
        }

        .greeting {
            font-size: 16px;
            margin-bottom: 25px;
            color: #2d3748;
            border-left: 4px solid #e5e5e5;
            padding-left: 15px;
        }

        .message {
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .message .highlight {
            color: #3182ce;
            font-weight: 500;
        }

        .instruction-text {
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #4a5568;
        }

        .button-container {
            text-align: center;
            margin: 35px 0;
        }

        .reset-button {
            display: inline-block;
            background-color: <?php echo esc_attr($settings['email_button_color']); ?>;
            color: white !important;
            text-decoration: none;
            padding: 15px 35px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .expiry-notice {
            font-size: 13px;
            color: #718096;
            text-align: center;
            margin-top: 15px;
        }

        .security-notice {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
        }

        .security-notice .icon {
            display: inline-block;
            margin-right: 8px;
        }

        .security-notice h3 {
            color: #2d3748;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .security-notice p {
            color: #4a5568;
            font-size: 13px;
            line-height: 1.5;
            margin: 0;
        }

        .signature {
            margin-top: 40px;
            padding-top: 25px;
            border-top: 1px solid #e2e8f0;
        }

        .signature-avatar {
            width: 40px;
            height: 40px;
            background-color: <?php echo esc_attr($settings['email_header_color']); ?>;
            border-radius: 50%;
            margin: 0 auto 15px auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .signature-avatar::before {
            content: "👤";
            font-size: 18px;
        }

        .signature-text {
            text-align: center;
            font-size: 14px;
            color: #2d3748;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .signature-subtitle {
            text-align: center;
            font-size: 12px;
            color: #718096;
        }

        .email-footer {
            background-color: #f7fafc;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .footer-dots {
            margin-bottom: 15px;
        }

        .footer-dots::before {
            content: "• • •";
            color: #cbd5e0;
            font-size: 16px;
        }

        .footer-text {
            font-size: 12px;
            color: #718096;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .footer-site {
            font-size: 12px;
            color: #4a5568;
            font-weight: 500;
        }

        .footer-email {
            font-size: 11px;
            color: #a0aec0;
            margin-top: 5px;
        }

        /* Responsivo */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 20px 10px;
            }

            .email-header,
            .email-body,
            .email-footer {
                padding: 30px 25px !important;
            }

            .email-header h1 {
                font-size: 22px !important;
            }

            .reset-button {
                display: block !important;
                width: 100% !important;
                padding: 15px !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">
                <?php if (!empty($settings['email_logo_url'])): ?>
                    <img src="<?php echo esc_url($settings['email_logo_url']); ?>" alt="<?php echo esc_attr($site_name); ?>" style="max-width: 80px; height: auto; margin-bottom: 20px;">
                <?php else: ?>
                    <div class="lock-icon"></div>
                <?php endif; ?>
                <h1>Redefinir Senha</h1>
                <p class="subtitle">Solicitação de redefinição de senha</p>
            </div>

            <!-- Body -->
            <div class="email-body">
                <!-- Greeting -->
                <div class="greeting">
                    <?php echo esc_html($settings['email_greeting']); ?>, <?php echo esc_html($user_data->display_name); ?>!
                </div>

                <!-- Main Message -->
                <div class="message">
                    <?php echo wp_kses_post($settings['email_main_text']); ?>
                </div>

                <div class="instruction-text">
                    <?php echo esc_html($settings['email_instruction_text']); ?>
                </div>

                <!-- CTA Button -->
                <div class="button-container">
                    <a href="<?php echo esc_url($reset_url); ?>" class="reset-button" style="background-color: <?php echo esc_attr($settings['email_button_color']); ?> !important;">
                        <?php echo esc_html($settings['email_button_text']); ?>
                    </a>
                    <div class="expiry-notice">
                        Este link expira em 24 horas
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <h3><span class="icon"><?php echo esc_html($settings['email_security_title']); ?></span></h3>
                    <p><?php echo esc_html($settings['email_security_text']); ?></p>
                </div>

                <!-- Alternative Link (se configurado) -->
                <?php if (!empty($settings['email_alternative_title'])): ?>
                <div class="security-notice" style="background-color: #f0f8ff; border-color: #b3d9ff;">
                    <h3><?php echo esc_html($settings['email_alternative_title']); ?></h3>
                    <p><?php echo esc_html($settings['email_alternative_text']); ?></p>
                    <p style="word-break: break-all; font-size: 12px; color: #666;"><?php echo esc_url($reset_url); ?></p>
                </div>
                <?php endif; ?>

                <!-- Help Text -->
                <?php if (!empty($settings['email_help_text'])): ?>
                <div style="margin-top: 25px; font-size: 14px; color: #666;">
                    <?php echo esc_html($settings['email_help_text']); ?>
                </div>
                <?php endif; ?>

                <!-- Signature -->
                <div class="signature">
                    <div class="signature-avatar"></div>
                    <div class="signature-text"><?php echo wp_kses_post($settings['email_signature']); ?></div>
                    <div class="signature-subtitle">Suporte Técnico</div>
                </div>
            </div>

            <!-- Footer -->
            <div class="email-footer">
                <div class="footer-dots"></div>
                <div class="footer-text">
                    <?php echo esc_html($settings['email_footer_text']); ?>
                </div>
                <div class="footer-site">
                    <?php echo esc_html($settings['email_from_name']); ?>
                </div>
                <div class="footer-email">
                    Este email foi enviado para <?php echo esc_html($user_data->user_email); ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>