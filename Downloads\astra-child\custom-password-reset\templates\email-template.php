<?php
/**
 * Template de Email para Redefinição de Senha
 * Template moderno, clean e minimal para emails de redefinição de senha
 */

if (!defined('ABSPATH')) {
    exit;
}

// Obter configurações
$settings = [
    'email_logo_url' => get_option('cpr_email_logo_url', ''),
    'email_header_color' => get_option('cpr_email_header_color', '#4a90e2'),
    'email_button_color' => get_option('cpr_email_button_color', '#4a90e2'),
    'email_footer_text' => get_option('cpr_email_footer_text', 'Este é um email automático, não responda.'),
    'email_from_name' => get_option('cpr_email_from_name', get_bloginfo('name')),
    'email_greeting' => get_option('cpr_email_greeting', 'Olá'),
    'email_main_text' => get_option('cpr_email_main_text', 'Recebemos uma solicitação para redefinir a senha da sua conta em {site_name}.'),
    'email_instruction_text' => get_option('cpr_email_instruction_text', 'Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:'),
    'email_button_text' => get_option('cpr_email_button_text', 'Redefinir Minha Senha'),
    'email_alternative_title' => get_option('cpr_email_alternative_title', 'Link não funcionando?'),
    'email_alternative_text' => get_option('cpr_email_alternative_text', 'Copie e cole este link no seu navegador:'),
    'email_security_title' => get_option('cpr_email_security_title', '🔒 Informações de Segurança'),
    'email_security_text' => get_option('cpr_email_security_text', 'Este link expira em 24 horas por motivos de segurança. Se você não solicitou esta redefinição, pode ignorar este email com segurança.'),
    'email_help_text' => get_option('cpr_email_help_text', 'Se você tiver alguma dúvida ou precisar de ajuda, entre em contato conosco.'),
    'email_signature' => get_option('cpr_email_signature', 'Atenciosamente,<br>Equipe {site_name}')
];

$site_name = get_bloginfo('name');
$site_url = get_site_url();

// Substituir placeholders
$settings['email_main_text'] = str_replace('{site_name}', $site_name, $settings['email_main_text']);
$settings['email_signature'] = str_replace('{site_name}', $site_name, $settings['email_signature']);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>Redefinir Senha - <?php echo esc_html($site_name); ?></title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, <?php echo esc_attr($settings['email_header_color']); ?>, <?php echo esc_attr($settings['email_header_color']); ?>dd);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }

        .email-logo {
            max-width: 150px;
            height: auto;
            margin-bottom: 20px;
        }

        .email-header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .email-header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .message {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #555555;
        }

        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, <?php echo esc_attr($settings['email_button_color']); ?>, <?php echo esc_attr($settings['email_button_color']); ?>dd);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
        }

        .button-container {
            text-align: center;
            margin: 30px 0;
        }

        .alternative-link {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }

        .alternative-link h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #495057;
        }

        .alternative-link p {
            font-size: 14px;
            color: #6c757d;
            word-break: break-all;
        }

        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }

        .security-notice h3 {
            color: #856404;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .security-notice p {
            color: #856404;
            font-size: 14px;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer-content {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.6;
        }

        .footer-content a {
            color: <?php echo esc_attr($settings['email_header_color']); ?>;
            text-decoration: none;
        }

        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            text-decoration: none;
        }

        /* Responsivo */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }

            .email-header,
            .email-body,
            .email-footer {
                padding: 20px !important;
            }

            .email-header h1 {
                font-size: 24px !important;
            }

            .reset-button {
                display: block !important;
                width: 100% !important;
                padding: 16px !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.6; color: #333333; background-color: #f8f9fa;">
    <!--[if mso]>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td>
    <![endif]-->
    
    <div style="width: 100%; background-color: #f7f8fc; padding: 30px 0; min-height: 100vh;">
        <div class="email-container" style="max-width: 580px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08); border-radius: 12px; overflow: hidden; border: 1px solid rgba(0,0,0,0.05);"
            <!-- Header -->
            <div style="background: linear-gradient(135deg, <?php echo esc_attr($settings['email_header_color']); ?> 0%, <?php echo esc_attr($settings['email_header_color']); ?>e6 100%); padding: 60px 50px; text-align: center; color: white; position: relative;">
                <?php if (!empty($settings['email_logo_url'])): ?>
                    <img src="<?php echo esc_url($settings['email_logo_url']); ?>" alt="<?php echo esc_attr($site_name); ?>" style="max-width: 100px; height: auto; margin-bottom: 30px; display: block; margin-left: auto; margin-right: auto; filter: brightness(0) invert(1);">
                <?php endif; ?>
                <h1 style="font-size: 26px; font-weight: 300; margin: 0 0 12px 0; color: white; letter-spacing: -0.5px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">Redefinir Senha</h1>
                <p style="font-size: 16px; margin: 0; opacity: 0.9; color: white; font-weight: 300;">Solicitação de redefinição de senha</p>
                <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0.2) 100%);"></div>
            </div>

            <!-- Body -->
            <div style="padding: 60px 50px;">
                <!-- Greeting -->
                <div style="font-size: 22px; margin-bottom: 35px; color: #2c3e50; font-weight: 300; letter-spacing: -0.3px; line-height: 1.3;">
                    <?php echo esc_html($settings['email_greeting']); ?>, <?php echo esc_html($user_data->display_name); ?>!
                </div>

                <!-- Main Message -->
                <div style="font-size: 17px; line-height: 1.8; margin-bottom: 45px; color: #555; font-weight: 400;">
                    <p style="margin: 0 0 24px 0;"><?php echo wp_kses_post($settings['email_main_text']); ?></p>
                    <p style="margin: 0; color: #666;"><?php echo esc_html($settings['email_instruction_text']); ?></p>
                </div>

                <!-- CTA Button -->
                <div style="text-align: center; margin: 50px 0;">
                    <!--[if mso]>
                    <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="<?php echo esc_url($reset_url); ?>" style="height:60px;v-text-anchor:middle;width:300px;" arcsize="8%" stroke="f" fillcolor="<?php echo esc_attr($settings['email_button_color']); ?>">
                        <w:anchorlock/>
                        <center style="color:#ffffff;font-family:sans-serif;font-size:17px;font-weight:500;"><?php echo esc_html($settings['email_button_text']); ?></center>
                    </v:roundrect>
                    <![endif]-->
                    <!--[if !mso]><!-->
                    <a href="<?php echo esc_url($reset_url); ?>" style="display: inline-block; background: linear-gradient(135deg, <?php echo esc_attr($settings['email_button_color']); ?> 0%, <?php echo esc_attr($settings['email_button_color']); ?>dd 100%); color: #ffffff !important; text-decoration: none; padding: 20px 40px; border-radius: 8px; font-weight: 500; font-size: 17px; text-align: center; letter-spacing: 0.3px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); transition: all 0.3s ease; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <?php echo esc_html($settings['email_button_text']); ?>
                    </a>
                    <!--<![endif]-->
                </div>

                <!-- Spacer -->
                <div style="height: 40px;"></div>

                <!-- Signature -->
                <div style="font-size: 16px; line-height: 1.7; color: #666; text-align: left;">
                    <div style="border-top: 1px solid #e9ecef; padding-top: 25px; margin-top: 30px;">
                        <p style="margin: 0; color: #2c3e50; font-weight: 400;"><?php echo wp_kses_post($settings['email_signature']); ?></p>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%); padding: 40px 50px; text-align: center; border-top: 1px solid #e9ecef;">
                <div style="font-size: 14px; color: #6c757d; line-height: 1.6;">
                    <p style="margin: 0 0 15px 0; font-weight: 400;"><?php echo esc_html($settings['email_footer_text']); ?></p>
                    <p style="margin: 0 0 20px 0;">
                        <a href="<?php echo esc_url($site_url); ?>" style="color: <?php echo esc_attr($settings['email_header_color']); ?>; text-decoration: none; font-weight: 500; font-size: 15px;"><?php echo esc_html($site_name); ?></a>
                    </p>
                    <div style="border-top: 2px solid #e9ecef; padding-top: 20px; margin-top: 20px;">
                        <p style="margin: 0; font-size: 12px; color: #adb5bd; font-weight: 300;">
                            Este email foi enviado para <strong style="color: #6c757d;"><?php echo esc_html($user_data->user_email); ?></strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!--[if mso]>
            </td>
        </tr>
    </table>
    <![endif]-->
</body>
</html>